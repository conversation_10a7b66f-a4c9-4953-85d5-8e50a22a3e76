<template>
  <div>
    <h4>Login Management</h4>
    <div v-if="!isLoading && !isError">
      <b-card>
        <!-- Personal Information Section -->
        <personal-info-settings
          :settings="userSettings.personalInfo"
          :isUpdatingProcessed="sections.personalInfo.isUpdatingProcessed"
          :isDisabled="sections.personalInfo.isDisabled"
          @save="savePersonalInfoSettings"
        />

        <!-- Password Section -->
        <password-settings
          :isUpdatingProcessed="sections.password.isUpdatingProcessed"
          :isDisabled="sections.password.isDisabled"
          @save="savePasswordSettings"
        />

        <!-- Default App Section -->
        <default-app-settings
          :settings="userSettings.defaultApp"
          :isUpdatingProcessed="sections.defaultApp.isUpdatingProcessed"
          :isDisabled="sections.defaultApp.isDisabled"
          @save="saveDefaultAppSettings"
        />
      </b-card>
    </div>
    <loader v-else-if="isLoading" class="my-4" size="lg"/>
    <div v-else class="text-center text-danger">
      <p>Failed to load user settings. Please try refreshing the page.</p>
    </div>
  </div>
</template>

<script>
import loader from '@/components/_shared/loader'
import personalInfoSettings from '@/components/users/loginManagement/personalInfoSettings'
import passwordSettings from '@/components/users/loginManagement/passwordSettings'
import defaultAppSettings from '@/components/users/loginManagement/defaultAppSettings'
import UserManagementService from '@/services/users/UserManagementService'
import { mapGetters } from 'vuex'

export default {
  name: 'login-management',
  metaInfo: {
    title: 'Login Management'
  },
  data () {
    return {
      isLoading: true,
      isError: false,
      userSettings: {
        personalInfo: {},
        defaultApp: {}
      },
      sections: {
        personalInfo: { isDisabled: false, isUpdatingProcessed: false },
        password: { isDisabled: false, isUpdatingProcessed: false },
        defaultApp: { isDisabled: false, isUpdatingProcessed: false }
      }
    }
  },
  computed: {
    ...mapGetters('users', ['userInfo']),
    currentUserId () {
      return (this.userInfo || {}).user?.id
    }
  },
  created () {
    this.loadUserSettings()
  },
  components: {
    'loader': loader,
    'personal-info-settings': personalInfoSettings,
    'password-settings': passwordSettings,
    'default-app-settings': defaultAppSettings
  },
  methods: {
    async loadUserSettings () {
      this.isLoading = true
      this.isError = false

      try {
        if (!this.currentUserId) {
          throw new Error('User ID not found')
        }

        const response = await UserManagementService.getUserSettings(this.currentUserId)

        if (response.data) {
          const userData = response.data
          
          this.userSettings = {
            personalInfo: {
              userName: userData.userName || '',
              firstName: userData.firstName || '',
              lastName: userData.lastName || '',
              email: userData.email || ''
            },
            defaultApp: userData.defaultApp || 0
          }
        }
      } catch (ex) {
        this.isError = true
        this.$toaster.error('Cannot load user settings')
        this.$logger.handleError(ex, 'Cannot load user settings')
      } finally {
        this.isLoading = false
      }
    },

    async savePersonalInfoSettings (personalInfoSettings) {
      const updatedUser = {
        ...this.userSettings,
        personalInfo: personalInfoSettings
      }
      await this.saveUserSettings('personalInfo', updatedUser)
    },

    async savePasswordSettings (passwordData) {
      await this.saveUserSettings('password', { password: passwordData.password })
    },

    async saveDefaultAppSettings (defaultAppSettings) {
      const updatedUser = {
        ...this.userSettings,
        defaultApp: defaultAppSettings.defaultApp
      }
      await this.saveUserSettings('defaultApp', updatedUser)
    },

    async saveUserSettings (sectionName, updatedData) {
      try {
        this.sections[sectionName].isUpdatingProcessed = true
        this.makeOtherSectionsDisabled(sectionName)

        // Prepare user data for API
        const userUpdateData = {
          id: this.currentUserId,
          userName: updatedData.personalInfo?.userName || this.userSettings.personalInfo.userName,
          firstName: updatedData.personalInfo?.firstName || this.userSettings.personalInfo.firstName,
          lastName: updatedData.personalInfo?.lastName || this.userSettings.personalInfo.lastName,
          email: updatedData.personalInfo?.email || this.userSettings.personalInfo.email,
          defaultApp: updatedData.defaultApp !== undefined ? updatedData.defaultApp : this.userSettings.defaultApp,
          password: updatedData.password || undefined
        }

        const response = await UserManagementService.updateUser(this.currentUserId, userUpdateData)
        
        if (response.data) {
          // Update local settings with response data
          if (sectionName !== 'password') {
            this.userSettings = {
              personalInfo: {
                userName: userUpdateData.userName,
                firstName: userUpdateData.firstName,
                lastName: userUpdateData.lastName,
                email: userUpdateData.email
              },
              defaultApp: userUpdateData.defaultApp
            }
          }
          
          this.$toaster.success(`${this.getSectionDisplayName(sectionName)} updated successfully`)
        }
      } catch (ex) {
        const errorMessage = (ex.response && ex.response.data && ex.response.data.executionResultMessage) || 
                            ex.message || 'Unknown error'
        this.$toaster.error(`Cannot update ${this.getSectionDisplayName(sectionName)}. ${errorMessage}`)
        this.$logger.handleError(ex, `Cannot update ${sectionName}`)
      } finally {
        this.sections[sectionName].isUpdatingProcessed = false
        this.makeAllSectionsEnabled()
      }
    },

    makeOtherSectionsDisabled (currentSection) {
      Object.keys(this.sections).forEach(section => {
        if (section !== currentSection) {
          this.sections[section].isDisabled = true
        }
      })
    },

    makeAllSectionsEnabled () {
      Object.keys(this.sections).forEach(section => {
        this.sections[section].isDisabled = false
      })
    },

    getSectionDisplayName (sectionName) {
      const displayNames = {
        personalInfo: 'Personal Information',
        password: 'Password',
        defaultApp: 'Default Application'
      }
      return displayNames[sectionName] || sectionName
    }
  }
}
</script>
