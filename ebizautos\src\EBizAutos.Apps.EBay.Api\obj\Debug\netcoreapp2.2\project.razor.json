{"SerializedFilePath": "d:\\Work\\ebizautos\\src\\EBizAutos.Apps.EBay.Api\\obj\\Debug\\netcoreapp2.2\\project.razor.json", "FilePath": "d:\\Work\\ebizautos\\src\\EBizAutos.Apps.EBay.Api\\EBizAutos.Apps.EBay.Api.csproj", "Configuration": {"ConfigurationName": "MVC-2.1", "LanguageVersion": "2.1", "Extensions": [{"ExtensionName": "MVC-2.1"}]}, "ProjectWorkspaceState": {"TagHelpers": [{"HashCode": 1891531461, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;a&gt; elements.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "a", "Attributes": [{"Name": "asp-action"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-controller"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-area"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-page"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-page-handler"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-fragment"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-host"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-protocol"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-route"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-all-route-data"}]}, {"TagName": "a", "Attributes": [{"Name": "asp-route-", "NameComparison": 1}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-action", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Action\">\r\n            <summary>\r\n            The name of the action method.\r\n            </summary>\r\n            <remarks>\r\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Route\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Page\" /> is non-<c>null</c>.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Action"}}, {"Kind": "ITagHelper", "Name": "asp-controller", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Controller\">\r\n            <summary>\r\n            The name of the controller.\r\n            </summary>\r\n            <remarks>\r\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Route\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Page\" /> is non-<c>null</c>.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Controller"}}, {"Kind": "ITagHelper", "Name": "asp-area", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Area\">\r\n            <summary>\r\n            The name of the area.\r\n            </summary>\r\n            <remarks>\r\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Route\" /> is non-<c>null</c>.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Area"}}, {"Kind": "ITagHelper", "Name": "asp-page", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Page\">\r\n            <summary>\r\n            The name of the page.\r\n            </summary>\r\n            <remarks>\r\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Route\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Action\" />, <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Controller\" />\r\n            is non-<c>null</c>.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Page"}}, {"Kind": "ITagHelper", "Name": "asp-page-handler", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.PageHandler\">\r\n            <summary>\r\n            The name of the page handler.\r\n            </summary>\r\n            <remarks>\r\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Route\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Action\" />, or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Controller\" />\r\n            is non-<c>null</c>.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>"}}, {"Kind": "ITagHelper", "Name": "asp-protocol", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Protocol\">\r\n            <summary>\r\n            The protocol for the URL, such as \"http\" or \"https\".\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Protocol"}}, {"Kind": "ITagHelper", "Name": "asp-host", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Host\">\r\n            <summary>\r\n            The host name.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Host"}}, {"Kind": "ITagHelper", "Name": "asp-fragment", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Fragment\">\r\n            <summary>\r\n            The URL fragment name.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Fragment"}}, {"Kind": "ITagHelper", "Name": "asp-route", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Route\">\r\n            <summary>\r\n            Name of the route.\r\n            </summary>\r\n            <remarks>\r\n            Must be <c>null</c> if one of <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Action\" />, <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Controller\" />, <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Area\" /> \r\n            or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.Page\" /> is non-<c>null</c>.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Route"}}, {"Kind": "ITagHelper", "Name": "asp-all-route-data", "TypeName": "System.Collections.Generic.IDictionary<System.String, System.String>", "IndexerNamePrefix": "asp-route-", "IndexerTypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper.RouteValues\">\r\n            <summary>\r\n            Additional parameters for the route.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "RouteValues"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.AnchorTagHelper", "Common.TypeNameIdentifier": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": 1214799720, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.TagHelper\" /> implementation targeting &lt;cache&gt; elements.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "cache"}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "priority", "TypeName": "Microsoft.Extensions.Caching.Memory.CacheItemPriority?", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelper.Priority\">\r\n            <summary>\r\n            Gets or sets the <see cref=\"T:Microsoft.Extensions.Caching.Memory.CacheItemPriority\" /> policy for the cache entry.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Priority"}}, {"Kind": "ITagHelper", "Name": "vary-by", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryBy\">\r\n            <summary>\r\n            Gets or sets a <see cref=\"T:System.String\" /> to vary the cached result by.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryBy"}}, {"Kind": "ITagHelper", "Name": "vary-by-header", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByHeader\">\r\n            <summary>\r\n            Gets or sets a comma-delimited set of HTTP request headers to vary the cached result by.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryByHeader"}}, {"Kind": "ITagHelper", "Name": "vary-by-query", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByQuery\">\r\n            <summary>\r\n            Gets or sets a comma-delimited set of query parameters to vary the cached result by.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryBy<PERSON>uery"}}, {"Kind": "ITagHelper", "Name": "vary-by-route", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByRoute\">\r\n            <summary>\r\n            Gets or sets a comma-delimited set of route data parameters to vary the cached result by.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryByRoute"}}, {"Kind": "ITagHelper", "Name": "vary-by-cookie", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByCookie\">\r\n            <summary>\r\n            Gets or sets a comma-delimited set of cookie names to vary the cached result by.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryByCookie"}}, {"Kind": "ITagHelper", "Name": "vary-by-user", "TypeName": "System.Boolean", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByUser\">\r\n            <summary>\r\n            Gets or sets a value that determines if the cached result is to be varied by the Identity for the logged in\r\n            <see cref=\"P:Microsoft.AspNetCore.Http.HttpContext.User\" />.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryByUser"}}, {"Kind": "ITagHelper", "Name": "vary-by-culture", "TypeName": "System.Boolean", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByCulture\">\r\n            <summary>\r\n            Gets or sets a value that determines if the cached result is to be varied by request culture.\r\n            <para>\r\n            Setting this to <c>true</c> would result in the result to be varied by <see cref=\"P:System.Globalization.CultureInfo.CurrentCulture\" />\r\n            and <see cref=\"P:System.Globalization.CultureInfo.CurrentUICulture\" />.\r\n            </para>\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryByCulture"}}, {"Kind": "ITagHelper", "Name": "expires-on", "TypeName": "System.DateTimeOffset?", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.ExpiresOn\">\r\n            <summary>\r\n            Gets or sets the exact <see cref=\"T:System.DateTimeOffset\" /> the cache entry should be evicted.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "ExpiresOn"}}, {"Kind": "ITagHelper", "Name": "expires-after", "TypeName": "System.TimeSpan?", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.ExpiresAfter\">\r\n            <summary>\r\n            Gets or sets the duration, from the time the cache entry was added, when it should be evicted.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "ExpiresAfter"}}, {"Kind": "ITagHelper", "Name": "expires-sliding", "TypeName": "System.TimeSpan?", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.ExpiresSliding\">\r\n            <summary>\r\n            Gets or sets the duration from last access that the cache entry should be evicted.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "ExpiresSliding"}}, {"Kind": "ITagHelper", "Name": "enabled", "TypeName": "System.Boolean", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.Enabled\">\r\n            <summary>\r\n            Gets or sets the value which determines if the tag helper is enabled or not.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Enabled"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelper", "Common.TypeNameIdentifier": "CacheTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": 189421719, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.DistributedCacheTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.DistributedCacheTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.TagHelper\" /> implementation targeting &lt;distributed-cache&gt; elements.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "distributed-cache", "Attributes": [{"Name": "name"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "name", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.DistributedCacheTagHelper.Name\">\r\n            <summary>\r\n            Gets or sets a unique name to discriminate cached entries.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Name"}}, {"Kind": "ITagHelper", "Name": "vary-by", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryBy\">\r\n            <summary>\r\n            Gets or sets a <see cref=\"T:System.String\" /> to vary the cached result by.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryBy"}}, {"Kind": "ITagHelper", "Name": "vary-by-header", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByHeader\">\r\n            <summary>\r\n            Gets or sets a comma-delimited set of HTTP request headers to vary the cached result by.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryByHeader"}}, {"Kind": "ITagHelper", "Name": "vary-by-query", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByQuery\">\r\n            <summary>\r\n            Gets or sets a comma-delimited set of query parameters to vary the cached result by.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryBy<PERSON>uery"}}, {"Kind": "ITagHelper", "Name": "vary-by-route", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByRoute\">\r\n            <summary>\r\n            Gets or sets a comma-delimited set of route data parameters to vary the cached result by.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryByRoute"}}, {"Kind": "ITagHelper", "Name": "vary-by-cookie", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByCookie\">\r\n            <summary>\r\n            Gets or sets a comma-delimited set of cookie names to vary the cached result by.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryByCookie"}}, {"Kind": "ITagHelper", "Name": "vary-by-user", "TypeName": "System.Boolean", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByUser\">\r\n            <summary>\r\n            Gets or sets a value that determines if the cached result is to be varied by the Identity for the logged in\r\n            <see cref=\"P:Microsoft.AspNetCore.Http.HttpContext.User\" />.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryByUser"}}, {"Kind": "ITagHelper", "Name": "vary-by-culture", "TypeName": "System.Boolean", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.VaryByCulture\">\r\n            <summary>\r\n            Gets or sets a value that determines if the cached result is to be varied by request culture.\r\n            <para>\r\n            Setting this to <c>true</c> would result in the result to be varied by <see cref=\"P:System.Globalization.CultureInfo.CurrentCulture\" />\r\n            and <see cref=\"P:System.Globalization.CultureInfo.CurrentUICulture\" />.\r\n            </para>\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "VaryByCulture"}}, {"Kind": "ITagHelper", "Name": "expires-on", "TypeName": "System.DateTimeOffset?", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.ExpiresOn\">\r\n            <summary>\r\n            Gets or sets the exact <see cref=\"T:System.DateTimeOffset\" /> the cache entry should be evicted.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "ExpiresOn"}}, {"Kind": "ITagHelper", "Name": "expires-after", "TypeName": "System.TimeSpan?", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.ExpiresAfter\">\r\n            <summary>\r\n            Gets or sets the duration, from the time the cache entry was added, when it should be evicted.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "ExpiresAfter"}}, {"Kind": "ITagHelper", "Name": "expires-sliding", "TypeName": "System.TimeSpan?", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.ExpiresSliding\">\r\n            <summary>\r\n            Gets or sets the duration from last access that the cache entry should be evicted.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "ExpiresSliding"}}, {"Kind": "ITagHelper", "Name": "enabled", "TypeName": "System.Boolean", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.CacheTagHelperBase.Enabled\">\r\n            <summary>\r\n            Gets or sets the value which determines if the tag helper is enabled or not.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Enabled"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.DistributedCacheTagHelper", "Common.TypeNameIdentifier": "DistributedCacheTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": 1977826817, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;environment&gt; elements that conditionally renders\r\n            content based on the current value of <see cref=\"P:Microsoft.AspNetCore.Hosting.IHostingEnvironment.EnvironmentName\" />.\r\n            If the environment is not listed in the specified <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Names\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Include\" />, \r\n            or if it is in <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Exclude\" />, the content will not be rendered.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "environment"}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "names", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Names\">\r\n            <summary>\r\n            A comma separated list of environment names in which the content should be rendered.\r\n            If the current environment is also in the <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Exclude\" /> list, the content will not be rendered.\r\n            </summary>\r\n            <remarks>\r\n            The specified environment names are compared case insensitively to the current value of\r\n            <see cref=\"P:Microsoft.AspNetCore.Hosting.IHostingEnvironment.EnvironmentName\" />.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Names"}}, {"Kind": "ITagHelper", "Name": "include", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Include\">\r\n            <summary>\r\n            A comma separated list of environment names in which the content should be rendered.\r\n            If the current environment is also in the <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Exclude\" /> list, the content will not be rendered.\r\n            </summary>\r\n            <remarks>\r\n            The specified environment names are compared case insensitively to the current value of\r\n            <see cref=\"P:Microsoft.AspNetCore.Hosting.IHostingEnvironment.EnvironmentName\" />.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Include"}}, {"Kind": "ITagHelper", "Name": "exclude", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper.Exclude\">\r\n            <summary>\r\n            A comma separated list of environment names in which the content will not be rendered.\r\n            </summary>\r\n            <remarks>\r\n            The specified environment names are compared case insensitively to the current value of\r\n            <see cref=\"P:Microsoft.AspNetCore.Hosting.IHostingEnvironment.EnvironmentName\" />.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Exclude"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.EnvironmentTagHelper", "Common.TypeNameIdentifier": "EnvironmentTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": 1616264267, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;button&gt; elements and &lt;input&gt; elements with\r\n            their <c>type</c> attribute set to <c>image</c> or <c>submit</c>.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "button", "Attributes": [{"Name": "asp-action"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-controller"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-area"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-page"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-page-handler"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-fragment"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-route"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-all-route-data"}]}, {"TagName": "button", "Attributes": [{"Name": "asp-route-", "NameComparison": 1}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-action"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-controller"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-area"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-page"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-page-handler"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-fragment"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-route"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-all-route-data"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "image", "ValueComparison": 1}, {"Name": "asp-route-", "NameComparison": 1}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-action"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-controller"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-area"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-page"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-page-handler"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-fragment"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-route"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-all-route-data"}]}, {"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "type", "Value": "submit", "ValueComparison": 1}, {"Name": "asp-route-", "NameComparison": 1}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-action", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.Action\">\r\n            <summary>\r\n            The name of the action method.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Action"}}, {"Kind": "ITagHelper", "Name": "asp-controller", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.Controller\">\r\n            <summary>\r\n            The name of the controller.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Controller"}}, {"Kind": "ITagHelper", "Name": "asp-area", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.Area\">\r\n            <summary>\r\n            The name of the area.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Area"}}, {"Kind": "ITagHelper", "Name": "asp-page", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.Page\">\r\n            <summary>\r\n            The name of the page.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Page"}}, {"Kind": "ITagHelper", "Name": "asp-page-handler", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.PageHandler\">\r\n            <summary>\r\n            The name of the page handler.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>"}}, {"Kind": "ITagHelper", "Name": "asp-fragment", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.Fragment\">\r\n            <summary>\r\n            Gets or sets the URL fragment.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Fragment"}}, {"Kind": "ITagHelper", "Name": "asp-route", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.Route\">\r\n            <summary>\r\n            Name of the route.\r\n            </summary>\r\n            <remarks>\r\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.Action\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.Controller\" /> is non-<c>null</c>.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Route"}}, {"Kind": "ITagHelper", "Name": "asp-all-route-data", "TypeName": "System.Collections.Generic.IDictionary<System.String, System.String>", "IndexerNamePrefix": "asp-route-", "IndexerTypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper.RouteValues\">\r\n            <summary>\r\n            Additional parameters for the route.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "RouteValues"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.FormActionTagHelper", "Common.TypeNameIdentifier": "FormActionTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": -215030899, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;form&gt; elements.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "form"}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-action", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.Action\">\r\n            <summary>\r\n            The name of the action method.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Action"}}, {"Kind": "ITagHelper", "Name": "asp-controller", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.Controller\">\r\n            <summary>\r\n            The name of the controller.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Controller"}}, {"Kind": "ITagHelper", "Name": "asp-area", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.Area\">\r\n            <summary>\r\n            The name of the area.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Area"}}, {"Kind": "ITagHelper", "Name": "asp-page", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.Page\">\r\n            <summary>\r\n            The name of the page.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Page"}}, {"Kind": "ITagHelper", "Name": "asp-page-handler", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.PageHandler\">\r\n            <summary>\r\n            The name of the page handler.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON><PERSON>"}}, {"Kind": "ITagHelper", "Name": "asp-antiforgery", "TypeName": "System.Boolean?", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.Antiforgery\">\r\n            <summary>\r\n            Whether the antiforgery token should be generated.\r\n            </summary>\r\n            <value>Defaults to <c>false</c> if user provides an <c>action</c> attribute\r\n            or if the <c>method</c> is <see cref=\"F:Microsoft.AspNetCore.Mvc.Rendering.FormMethod.Get\" />; <c>true</c> otherwise.</value>\r\n        </member>", "Metadata": {"Common.PropertyName": "Antiforgery"}}, {"Kind": "ITagHelper", "Name": "asp-fragment", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.Fragment\">\r\n            <summary>\r\n            Gets or sets the URL fragment.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Fragment"}}, {"Kind": "ITagHelper", "Name": "asp-route", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.Route\">\r\n            <summary>\r\n            Name of the route.\r\n            </summary>\r\n            <remarks>\r\n            Must be <c>null</c> if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.Action\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.Controller\" /> is non-<c>null</c>.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Route"}}, {"Kind": "ITagHelper", "Name": "asp-all-route-data", "TypeName": "System.Collections.Generic.IDictionary<System.String, System.String>", "IndexerNamePrefix": "asp-route-", "IndexerTypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper.RouteValues\">\r\n            <summary>\r\n            Additional parameters for the route.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "RouteValues"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.FormTagHelper", "Common.TypeNameIdentifier": "FormTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": -1954489700, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.ImageTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.ImageTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;img&gt; elements that supports file versioning.\r\n            </summary>\r\n            <remarks>\r\n            The tag helper won't process for cases with just the 'src' attribute.\r\n            </remarks>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "img", "TagStructure": 2, "Attributes": [{"Name": "asp-append-version"}, {"Name": "src"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "src", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ImageTagHelper.Src\">\r\n            <summary>\r\n            Source of the image.\r\n            </summary>\r\n            <remarks>\r\n            Passed through to the generated HTML in all cases.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Src"}}, {"Kind": "ITagHelper", "Name": "asp-append-version", "TypeName": "System.Boolean", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ImageTagHelper.AppendVersion\">\r\n            <summary>\r\n            Value indicating if file version should be appended to the src urls.\r\n            </summary>\r\n            <remarks>\r\n            If <c>true</c> then a query string \"v\" with the encoded content of the file is added.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "AppendVersion"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.ImageTagHelper", "Common.TypeNameIdentifier": "ImageTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": -62241011, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;input&gt; elements with an <c>asp-for</c> attribute.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "input", "TagStructure": 2, "Attributes": [{"Name": "asp-for"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.For\">\r\n            <summary>\r\n            An expression to be evaluated against the current model.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "For"}}, {"Kind": "ITagHelper", "Name": "asp-format", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.Format\">\r\n            <summary>\r\n            The format string (see https://msdn.microsoft.com/en-us/library/txafckwd.aspx) used to format the\r\n            <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.For\" /> result. Sets the generated \"value\" attribute to that formatted string.\r\n            </summary>\r\n            <remarks>\r\n            Not used if the provided (see <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.InputTypeName\" />) or calculated \"type\" attribute value is\r\n            <c>checkbox</c>, <c>password</c>, or <c>radio</c>. That is, <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.Format\" /> is used when calling\r\n            <see cref=\"M:Microsoft.AspNetCore.Mvc.ViewFeatures.IHtmlGenerator.GenerateTextBox(Microsoft.AspNetCore.Mvc.Rendering.ViewContext,Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExplorer,System.String,System.Object,System.String,System.Object)\" />.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Format"}}, {"Kind": "ITagHelper", "Name": "type", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.InputTypeName\">\r\n            <summary>\r\n            The type of the &lt;input&gt; element.\r\n            </summary>\r\n            <remarks>\r\n            Passed through to the generated HTML in all cases. Also used to determine the <see cref=\"T:Microsoft.AspNetCore.Mvc.ViewFeatures.IHtmlGenerator\" />\r\n            helper to call and the default <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.Format\" /> value. A default <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.Format\" /> is not calculated\r\n            if the provided (see <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.InputTypeName\" />) or calculated \"type\" attribute value is <c>checkbox</c>,\r\n            <c>hidden</c>, <c>password</c>, or <c>radio</c>.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "InputTypeName"}}, {"Kind": "ITagHelper", "Name": "name", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.Name\">\r\n            <summary>\r\n            The name of the &lt;input&gt; element.\r\n            </summary>\r\n            <remarks>\r\n            Passed through to the generated HTML in all cases. Also used to determine whether <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.For\" /> is\r\n            valid with an empty <see cref=\"P:Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression.Name\" />.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Name"}}, {"Kind": "ITagHelper", "Name": "value", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.Value\">\r\n            <summary>\r\n            The value of the &lt;input&gt; element.\r\n            </summary>\r\n            <remarks>\r\n            Passed through to the generated HTML in all cases. Also used to determine the generated \"checked\" attribute\r\n            if <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper.InputTypeName\" /> is \"radio\". Must not be <c>null</c> in that case.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Value"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.InputTagHelper", "Common.TypeNameIdentifier": "InputTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": -1141720259, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.LabelTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.LabelTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;label&gt; elements with an <c>asp-for</c> attribute.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "label", "Attributes": [{"Name": "asp-for"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LabelTagHelper.For\">\r\n            <summary>\r\n            An expression to be evaluated against the current model.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "For"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.LabelTagHelper", "Common.TypeNameIdentifier": "LabelTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": -1560248652, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;link&gt; elements that supports fallback href paths.\r\n            </summary>\r\n            <remarks>\r\n            The tag helper won't process for cases with just the 'href' attribute.\r\n            </remarks>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-href-include"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-href-exclude"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-href"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-href-include"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-href-exclude"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-test-class"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-test-property"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-fallback-test-value"}]}, {"TagName": "link", "TagStructure": 2, "Attributes": [{"Name": "asp-append-version"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "href", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.Href\">\r\n            <summary>\r\n            Address of the linked resource.\r\n            </summary>\r\n            <remarks>\r\n            Passed through to the generated HTML in all cases.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "<PERSON><PERSON><PERSON>"}}, {"Kind": "ITagHelper", "Name": "asp-href-include", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.HrefInclude\">\r\n            <summary>\r\n            A comma separated list of globbed file patterns of CSS stylesheets to load.\r\n            The glob patterns are assessed relative to the application's 'webroot' setting.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "HrefInclude"}}, {"Kind": "ITagHelper", "Name": "asp-href-exclude", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.HrefExclude\">\r\n            <summary>\r\n            A comma separated list of globbed file patterns of CSS stylesheets to exclude from loading.\r\n            The glob patterns are assessed relative to the application's 'webroot' setting.\r\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.HrefInclude\" />.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "HrefExclude"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-href", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHref\">\r\n            <summary>\r\n            The URL of a CSS stylesheet to fallback to in the case the primary one fails.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "FallbackHref"}}, {"Kind": "ITagHelper", "Name": "asp-suppress-fallback-integrity", "TypeName": "System.Boolean", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.SuppressFallbackIntegrity\">\r\n            <summary>\r\n            Boolean value that determines if an integrity hash will be compared with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHref\" /> value.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "SuppressFallbackIntegrity"}}, {"Kind": "ITagHelper", "Name": "asp-append-version", "TypeName": "System.Boolean?", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.AppendVersion\">\r\n            <summary>\r\n            Value indicating if file version should be appended to the href urls.\r\n            </summary>\r\n            <remarks>\r\n            If <c>true</c> then a query string \"v\" with the encoded content of the file is added.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "AppendVersion"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-href-include", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHrefInclude\">\r\n            <summary>\r\n            A comma separated list of globbed file patterns of CSS stylesheets to fallback to in the case the primary\r\n            one fails.\r\n            The glob patterns are assessed relative to the application's 'webroot' setting.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "FallbackHrefInclude"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-href-exclude", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHrefExclude\">\r\n            <summary>\r\n            A comma separated list of globbed file patterns of CSS stylesheets to exclude from the fallback list, in\r\n            the case the primary one fails.\r\n            The glob patterns are assessed relative to the application's 'webroot' setting.\r\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHrefInclude\" />.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "FallbackHrefExclude"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-test-class", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestClass\">\r\n            <summary>\r\n            The class name defined in the stylesheet to use for the fallback test.\r\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestProperty\" /> and <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestValue\" />,\r\n            and either <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHref\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHrefInclude\" />.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "FallbackTestClass"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-test-property", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestProperty\">\r\n            <summary>\r\n            The CSS property name to use for the fallback test.\r\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestClass\" /> and <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestValue\" />,\r\n            and either <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHref\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHrefInclude\" />.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "FallbackTestProperty"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-test-value", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestValue\">\r\n            <summary>\r\n            The CSS property value to use for the fallback test.\r\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestClass\" /> and <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackTestProperty\" />,\r\n            and either <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHref\" /> or <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper.FallbackHrefInclude\" />.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "FallbackTestValue"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.LinkTagHelper", "Common.TypeNameIdentifier": "LinkTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": 2068893150, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;option&gt; elements.\r\n            </summary>\r\n            <remarks>\r\n            This <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> works in conjunction with <see cref=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper\" />. It reads elements\r\n            content but does not modify that content. The only modification it makes is to add a <c>selected</c> attribute\r\n            in some cases.\r\n            </remarks>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "option"}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "value", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper.Value\">\r\n            <summary>\r\n            Specifies a value for the &lt;option&gt; element.\r\n            </summary>\r\n            <remarks>\r\n            Passed through to the generated HTML in all cases.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Value"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.OptionTagHelper", "Common.TypeNameIdentifier": "OptionTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": 1367124945, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper\">\r\n            <summary>\r\n            Renders a partial view.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "partial", "TagStructure": 2, "Attributes": [{"Name": "name"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "name", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.Name\">\r\n            <summary>\r\n            The name or path of the partial view that is rendered to the response.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Name"}}, {"Kind": "ITagHelper", "Name": "for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.For\">\r\n            <summary>\r\n            An expression to be evaluated against the current model. Cannot be used together with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.Model\" />.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "For"}}, {"Kind": "ITagHelper", "Name": "model", "TypeName": "System.Object", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.Model\">\r\n            <summary>\r\n            The model to pass into the partial view. Cannot be used together with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.For\" />.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Model"}}, {"Kind": "ITagHelper", "Name": "optional", "TypeName": "System.Boolean", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.Optional\">\r\n            <summary>\r\n            When optional, executing the tag helper will no-op if the view cannot be located. \r\n            Otherwise will throw stating the view could not be found.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Optional"}}, {"Kind": "ITagHelper", "Name": "fallback-name", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.FallbackName\">\r\n            <summary>\r\n            View to lookup if the view specified by <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.Name\" /> cannot be located.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "FallbackName"}}, {"Kind": "ITagHelper", "Name": "view-data", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary", "IndexerNamePrefix": "view-data-", "IndexerTypeName": "System.Object", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper.ViewData\">\r\n            <summary>\r\n            A <see cref=\"T:Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary\" /> to pass into the partial view.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "ViewData"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.PartialTagHelper", "Common.TypeNameIdentifier": "PartialTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": -1477085047, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;script&gt; elements that supports fallback src paths.\r\n            </summary>\r\n            <remarks>\r\n            The tag helper won't process for cases with just the 'src' attribute.\r\n            </remarks>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "script", "Attributes": [{"Name": "asp-src-include"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-src-exclude"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-fallback-src"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-fallback-src-include"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-fallback-src-exclude"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-fallback-test"}]}, {"TagName": "script", "Attributes": [{"Name": "asp-append-version"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "src", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.Src\">\r\n            <summary>\r\n            Address of the external script to use.\r\n            </summary>\r\n            <remarks>\r\n            Passed through to the generated HTML in all cases.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Src"}}, {"Kind": "ITagHelper", "Name": "asp-src-include", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.SrcInclude\">\r\n            <summary>\r\n            A comma separated list of globbed file patterns of JavaScript scripts to load.\r\n            The glob patterns are assessed relative to the application's 'webroot' setting.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "SrcInclude"}}, {"Kind": "ITagHelper", "Name": "asp-src-exclude", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.SrcExclude\">\r\n            <summary>\r\n            A comma separated list of globbed file patterns of JavaScript scripts to exclude from loading.\r\n            The glob patterns are assessed relative to the application's 'webroot' setting.\r\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.SrcInclude\" />.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "SrcExclude"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-src", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.FallbackSrc\">\r\n            <summary>\r\n            The URL of a Script tag to fallback to in the case the primary one fails.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "FallbackSrc"}}, {"Kind": "ITagHelper", "Name": "asp-suppress-fallback-integrity", "TypeName": "System.Boolean", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.SuppressFallbackIntegrity\">\r\n            <summary>\r\n            Boolean value that determines if an integrity hash will be compared with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.FallbackSrc\" /> value.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "SuppressFallbackIntegrity"}}, {"Kind": "ITagHelper", "Name": "asp-append-version", "TypeName": "System.Boolean?", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.AppendVersion\">\r\n            <summary>\r\n            Value indicating if file version should be appended to src urls.\r\n            </summary>\r\n            <remarks>\r\n            A query string \"v\" with the encoded content of the file is added.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "AppendVersion"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-src-include", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.FallbackSrcInclude\">\r\n            <summary>\r\n            A comma separated list of globbed file patterns of JavaScript scripts to fallback to in the case the\r\n            primary one fails.\r\n            The glob patterns are assessed relative to the application's 'webroot' setting.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "FallbackSrcInclude"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-src-exclude", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.FallbackSrcExclude\">\r\n            <summary>\r\n            A comma separated list of globbed file patterns of JavaScript scripts to exclude from the fallback list, in\r\n            the case the primary one fails.\r\n            The glob patterns are assessed relative to the application's 'webroot' setting.\r\n            Must be used in conjunction with <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.FallbackSrcInclude\" />.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "FallbackSrcExclude"}}, {"Kind": "ITagHelper", "Name": "asp-fallback-test", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper.FallbackTestExpression\">\r\n            <summary>\r\n            The script method defined in the primary script to use for the fallback test.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "FallbackTestExpression"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.ScriptTagHelper", "Common.TypeNameIdentifier": "ScriptTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": 1285947204, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;select&gt; elements with <c>asp-for</c> and/or\r\n            <c>asp-items</c> attribute(s).\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "select", "Attributes": [{"Name": "asp-for"}]}, {"TagName": "select", "Attributes": [{"Name": "asp-items"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper.For\">\r\n            <summary>\r\n            An expression to be evaluated against the current model.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "For"}}, {"Kind": "ITagHelper", "Name": "asp-items", "TypeName": "System.Collections.Generic.IEnumerable<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper.Items\">\r\n            <summary>\r\n            A collection of <see cref=\"T:Microsoft.AspNetCore.Mvc.Rendering.SelectListItem\" /> objects used to populate the &lt;select&gt; element with\r\n            &lt;optgroup&gt; and &lt;option&gt; elements.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "Items"}}, {"Kind": "ITagHelper", "Name": "name", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper.Name\">\r\n            <summary>\r\n            The name of the &lt;input&gt; element.\r\n            </summary>\r\n            <remarks>\r\n            Passed through to the generated HTML in all cases. Also used to determine whether <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper.For\" /> is\r\n            valid with an empty <see cref=\"P:Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression.Name\" />.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Name"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.SelectTagHelper", "Common.TypeNameIdentifier": "SelectTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": 1703077228, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting &lt;textarea&gt; elements with an <c>asp-for</c> attribute.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "textarea", "Attributes": [{"Name": "asp-for"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper.For\">\r\n            <summary>\r\n            An expression to be evaluated against the current model.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "For"}}, {"Kind": "ITagHelper", "Name": "name", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper.Name\">\r\n            <summary>\r\n            The name of the &lt;input&gt; element.\r\n            </summary>\r\n            <remarks>\r\n            Passed through to the generated HTML in all cases. Also used to determine whether <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper.For\" /> is\r\n            valid with an empty <see cref=\"P:Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression.Name\" />.\r\n            </remarks>\r\n        </member>", "Metadata": {"Common.PropertyName": "Name"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.TextAreaTagHelper", "Common.TypeNameIdentifier": "TextAreaTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": 1990022247, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.ValidationMessageTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.ValidationMessageTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting any HTML element with an <c>asp-validation-for</c>\r\n            attribute.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "span", "Attributes": [{"Name": "asp-validation-for"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-validation-for", "TypeName": "Microsoft.AspNetCore.Mvc.ViewFeatures.ModelExpression", "Metadata": {"Common.PropertyName": "For"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.ValidationMessageTagHelper", "Common.TypeNameIdentifier": "ValidationMessageTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": 1230595052, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper", "AssemblyName": "Microsoft.AspNetCore.Mvc.TagHelpers", "Documentation": "<member name=\"T:Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper\">\r\n            <summary>\r\n            <see cref=\"T:Microsoft.AspNetCore.Razor.TagHelpers.ITagHelper\" /> implementation targeting any HTML element with an <c>asp-validation-summary</c>\r\n            attribute.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "div", "Attributes": [{"Name": "asp-validation-summary"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-validation-summary", "TypeName": "Microsoft.AspNetCore.Mvc.Rendering.ValidationSummary", "IsEnum": true, "Documentation": "<member name=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper.ValidationSummary\">\r\n            <summary>\r\n            If <see cref=\"F:Microsoft.AspNetCore.Mvc.Rendering.ValidationSummary.All\" /> or <see cref=\"F:Microsoft.AspNetCore.Mvc.Rendering.ValidationSummary.ModelOnly\" />, appends a validation\r\n            summary. Otherwise (<see cref=\"F:Microsoft.AspNetCore.Mvc.Rendering.ValidationSummary.None\" />, the default), this tag helper does nothing.\r\n            </summary>\r\n            <exception cref=\"T:System.ArgumentException\">\r\n            Thrown if setter is called with an undefined <see cref=\"P:Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper.ValidationSummary\" /> value e.g.\r\n            <c>(ValidationSummary)23</c>.\r\n            </exception>\r\n        </member>", "Metadata": {"Common.PropertyName": "ValidationSummary"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.Mvc.TagHelpers.ValidationSummaryTagHelper", "Common.TypeNameIdentifier": "ValidationSummaryTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.Mvc.TagHelpers", "Runtime.Name": "ITagHelper"}}, {"HashCode": -2107402650, "Kind": "ITagHelper", "Name": "Microsoft.AspNetCore.SpaServices.Prerendering.PrerenderTagHelper", "AssemblyName": "Microsoft.AspNetCore.SpaServices", "Documentation": "<member name=\"T:Microsoft.AspNetCore.SpaServices.Prerendering.PrerenderTagHelper\">\r\n            <summary>\r\n            A tag helper for prerendering JavaScript applications on the server.\r\n            </summary>\r\n        </member>", "CaseSensitive": false, "TagMatchingRules": [{"TagName": "*", "Attributes": [{"Name": "asp-prerender-module"}]}], "BoundAttributes": [{"Kind": "ITagHelper", "Name": "asp-prerender-module", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.SpaServices.Prerendering.PrerenderTagHelper.ModuleName\">\r\n            <summary>\r\n            Specifies the path to the JavaScript module containing prerendering code.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "ModuleName"}}, {"Kind": "ITagHelper", "Name": "asp-prerender-export", "TypeName": "System.String", "Documentation": "<member name=\"P:Microsoft.AspNetCore.SpaServices.Prerendering.PrerenderTagHelper.ExportName\">\r\n            <summary>\r\n            If set, specifies the name of the CommonJS export that is the prerendering function to execute.\r\n            If not set, the JavaScript module's default CommonJS export must itself be the prerendering function.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "ExportName"}}, {"Kind": "ITagHelper", "Name": "asp-prerender-data", "TypeName": "System.Object", "Documentation": "<member name=\"P:Microsoft.AspNetCore.SpaServices.Prerendering.PrerenderTagHelper.CustomDataParameter\">\r\n            <summary>\r\n            An optional JSON-serializable parameter to be supplied to the prerendering code.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "CustomDataParameter"}}, {"Kind": "ITagHelper", "Name": "asp-prerender-timeout", "TypeName": "System.Int32", "Documentation": "<member name=\"P:Microsoft.AspNetCore.SpaServices.Prerendering.PrerenderTagHelper.TimeoutMillisecondsParameter\">\r\n            <summary>\r\n            The maximum duration to wait for prerendering to complete.\r\n            </summary>\r\n        </member>", "Metadata": {"Common.PropertyName": "TimeoutMillisecondsParameter"}}], "Metadata": {"Common.TypeName": "Microsoft.AspNetCore.SpaServices.Prerendering.PrerenderTagHelper", "Common.TypeNameIdentifier": "PrerenderTagHelper", "Common.TypeNamespace": "Microsoft.AspNetCore.SpaServices.Prerendering", "Runtime.Name": "ITagHelper"}}], "CSharpLanguageVersion": 703}, "RootNamespace": "EBizAutos.Apps.EBay.Api", "Documents": [], "SerializationFormat": "0.3"}