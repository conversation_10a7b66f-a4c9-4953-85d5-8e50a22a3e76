<template>
  <div class='mb-4'>
    <editSettingsHelper
      @cancel="cancel"
      @save="saveSettings"
      @changeMode="changeMode"
      title="Default Application"
      :isLoading="isUpdatingProcessed"
      :isDisabled="isDisabled"
      :isViewMode="isViewMode"
    >
      <div slot="settings-content">
        <ValidationObserver ref="validator">
          <detail-row fixedPayloadWidth>
            <span slot="title">Default App:</span>
            <div slot="payload">
              <ValidationProvider
                name="Default App"
                rules="required"
                v-slot="{ errors }"
              >
                <b-form-select
                  v-model="localSettings.defaultApp"
                  :options="appOptions"
                  class="login-settings-select-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
              <small class="form-text text-muted">Select the application to open by default after login</small>
            </div>
          </detail-row>
        </ValidationObserver>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import { ValidationObserver, ValidationProvider } from 'vee-validate'
import { appTypes } from '@/shared/users/constants'

export default {
  name: 'default-app-settings',
  props: {
    settings: {
      type: [Number, String],
      required: true
    },
    isUpdatingProcessed: {
      type: Boolean,
      default: false
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isViewMode: true,
      localSettings: {
        defaultApp: 0
      }
    }
  },
  computed: {
    appOptions () {
      return Object.values(appTypes).map(app => ({
        value: app.value,
        text: app.text
      }))
    }
  },
  created () {
    this.initData()
  },
  watch: {
    settings: {
      handler () {
        this.initData()
      }
    }
  },
  components: {
    'detail-row': detailRow,
    'editSettingsHelper': editSettingsHelper,
    ValidationObserver,
    ValidationProvider
  },
  methods: {
    initData () {
      this.localSettings.defaultApp = this.settings || 0
    },

    async saveSettings () {
      const isValid = await this.$refs.validator.validate()
      if (isValid) {
        this.$emit('save', { defaultApp: this.localSettings.defaultApp })
        this.isViewMode = true
      }
    },

    changeMode (mode) {
      this.isViewMode = mode
    },

    cancel () {
      this.initData()
      this.changeMode(true)
      this.$refs.validator.reset()
    }
  }
}
</script>

<style>
.login-settings-select-input {
  width: 300px;
}
</style>
