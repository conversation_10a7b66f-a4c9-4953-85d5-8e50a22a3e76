<template>
  <div class='mb-4'>
    <editSettingsHelper
      @cancel="cancel"
      @save="saveSettings"
      @changeMode="changeMode"
      title="Password"
      :isLoading="isUpdatingProcessed"
      :isDisabled="isDisabled"
      :isViewMode="isViewMode"
    >
      <div slot="settings-content">
        <ValidationObserver ref="validator">
          <detail-row fixedPayloadWidth>
            <span slot="title">New Password:</span>
            <div slot="payload">
              <ValidationProvider
                name="Password"
                rules="required|min:6"
                v-slot="{ errors }"
                vid="password"
              >
                <b-form-input
                  v-model="localSettings.password"
                  type="password"
                  class="login-settings-text-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                  placeholder="Enter new password"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
              <small class="form-text text-muted">Password must be at least 6 characters long</small>
            </div>
          </detail-row>

          <detail-row fixedPayloadWidth>
            <span slot="title">Confirm Password:</span>
            <div slot="payload">
              <ValidationProvider
                name="Confirm Password"
                rules="required|confirmed:password"
                v-slot="{ errors }"
              >
                <b-form-input
                  v-model="localSettings.confirmPassword"
                  type="password"
                  class="login-settings-text-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                  placeholder="Confirm new password"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
            </div>
          </detail-row>
        </ValidationObserver>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import { ValidationObserver, ValidationProvider } from 'vee-validate'

export default {
  name: 'password-settings',
  props: {
    isUpdatingProcessed: {
      type: Boolean,
      default: false
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isViewMode: true,
      localSettings: {
        password: '',
        confirmPassword: ''
      }
    }
  },
  components: {
    'detail-row': detailRow,
    'editSettingsHelper': editSettingsHelper,
    ValidationObserver,
    ValidationProvider
  },
  methods: {
    async saveSettings () {
      const isValid = await this.$refs.validator.validate()
      if (isValid) {
        this.$emit('save', { password: this.localSettings.password })
        this.isViewMode = true
        this.clearPasswordFields()
      }
    },

    changeMode (mode) {
      this.isViewMode = mode
      if (!mode) {
        this.clearPasswordFields()
      }
    },

    cancel () {
      this.clearPasswordFields()
      this.changeMode(true)
      this.$refs.validator.reset()
    },

    clearPasswordFields () {
      this.localSettings.password = ''
      this.localSettings.confirmPassword = ''
    }
  }
}
</script>

<style>
.login-settings-text-input {
  width: 300px;
}
</style>
