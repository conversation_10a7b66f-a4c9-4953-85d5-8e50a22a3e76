<template>
  <div class='mb-4'>
    <editSettingsHelper
      @cancel="cancel"
      @save="saveSettings"
      @changeMode="changeMode"
      title="Personal Information"
      :isLoading="isUpdatingProcessed"
      :isDisabled="isDisabled"
      :isViewMode="isViewMode"
    >
      <div slot="settings-content">
        <ValidationObserver ref="validator">
          <detail-row fixedPayloadWidth>
            <span slot="title">Login:</span>
            <div slot="payload">
              <ValidationProvider
                name="Login"
                rules="required"
                v-slot="{ errors }"
              >
                <b-form-input
                  v-model="localSettings.userName"
                  type="text"
                  class="login-settings-text-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
            </div>
          </detail-row>

          <detail-row fixedPayloadWidth>
            <span slot="title">First Name:</span>
            <div slot="payload">
              <ValidationProvider
                name="First Name"
                rules="required"
                v-slot="{ errors }"
              >
                <b-form-input
                  v-model="localSettings.firstName"
                  type="text"
                  class="login-settings-text-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
            </div>
          </detail-row>

          <detail-row fixedPayloadWidth>
            <span slot="title">Last Name:</span>
            <div slot="payload">
              <ValidationProvider
                name="Last Name"
                rules="required"
                v-slot="{ errors }"
              >
                <b-form-input
                  v-model="localSettings.lastName"
                  type="text"
                  class="login-settings-text-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
            </div>
          </detail-row>

          <detail-row fixedPayloadWidth>
            <span slot="title">Email:</span>
            <div slot="payload">
              <ValidationProvider
                name="Email"
                rules="required|email"
                v-slot="{ errors }"
              >
                <b-form-input
                  v-model="localSettings.email"
                  type="email"
                  class="login-settings-text-input"
                  :disabled="isViewMode"
                  :state="errors.length > 0 ? false : null"
                />
                <b-form-invalid-feedback>{{ errors[0] }}</b-form-invalid-feedback>
              </ValidationProvider>
            </div>
          </detail-row>
        </ValidationObserver>
      </div>
    </editSettingsHelper>
  </div>
</template>

<script>
import detailRow from '@/components/details/helpers/detailRow'
import editSettingsHelper from '@/components/_shared/editSettingsHelper'
import { ValidationObserver, ValidationProvider } from 'vee-validate'

export default {
  name: 'personal-info-settings',
  props: {
    settings: {
      type: Object,
      required: true
    },
    isUpdatingProcessed: {
      type: Boolean,
      default: false
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isViewMode: true,
      localSettings: {}
    }
  },
  created () {
    this.initData()
  },
  watch: {
    settings: {
      handler () {
        this.initData()
      },
      deep: true
    }
  },
  components: {
    'detail-row': detailRow,
    'editSettingsHelper': editSettingsHelper,
    ValidationObserver,
    ValidationProvider
  },
  methods: {
    initData () {
      if (this.settings) {
        this.localSettings = { ...this.settings }
      }
    },

    async saveSettings () {
      const isValid = await this.$refs.validator.validate()
      if (isValid) {
        this.$emit('save', this.localSettings)
        this.isViewMode = true
      }
    },

    changeMode (mode) {
      this.isViewMode = mode
    },

    cancel () {
      this.initData()
      this.changeMode(true)
      this.$refs.validator.reset()
    }
  }
}
</script>

<style>
.login-settings-text-input {
  width: 300px;
}
</style>
